import { fireEvent, render, screen } from '@testing-library/react';

import { AvatarGroup, AvatarGroupProps } from './AvatarGroup.Component';

interface MockedDropdownWithSearch extends AvatarGroupProps {
  children: React.ReactNode;
}

jest.mock('../avatar/Avatar.Component', () => ({
  __esModule: true,
  Avatar: ({ ...props }) => <div data-testid="avatar" {...props} />,
}));

jest.mock('../dropdown/DropdownWithSearch.Component', () => ({
  __esModule: true,
  DropdownWithSearch: ({ children, ...props }: MockedDropdownWithSearch) => (
    <div data-testid="dropdown" {...props}>
      {children}
    </div>
  ),
}));

describe('AvatarGroup Component', () => {
  const mockAvatarProps = { size: 50 };
  const mockDropdownProps = {
    options: [{ id: '1', name: 'Test 1' }, { id: '2', name: 'Test 2' }],
    setSelectedOption: jest.fn(),
  };

  it('should render AvatarGroup with no avatars when itemsSelected is empty', () => {
    render(
      <AvatarGroup
        itemsSelected={[]}
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );
    expect(screen.getByTestId('avatar-group')).toBeInTheDocument();
  });

  it('should render avatars when itemsSelected has items', () => {
    render(
      <AvatarGroup
        itemsSelected={[{ id: '1', name: 'Test 1' }]}
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );
    expect(screen.getAllByTestId('avatar')[0]).toBeInTheDocument();
  });

  it('should render correct number of avatars based on maxToShow', () => {
    render(
      <AvatarGroup
        itemsSelected={[
          { id: '1', name: 'Test 1' },
          { id: '2', name: 'Test 2' },
          { id: '3', name: 'Test 3' },
          { id: '4', name: 'Test 4' },
        ]}
        maxToShow={3}
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );
    const avatars = screen.getAllByTestId('avatar-group-item');
    expect(avatars.length).toBe(3);
  });

  it('should render remaining count', () => {
    render(
      <AvatarGroup
        itemsSelected={[
          { id: '1', name: 'Test 1' },
          { id: '2', name: 'Test 2' },
          { id: '3', name: 'Test 3' },
          { id: '4', name: 'Test 4' },
        ]}
        maxToShow={3}
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );
    expect(screen.getByText('+1')).toBeInTheDocument();
  });

  it('should render "No existen datos" when readOnly is true and itemsSelected is empty', () => {
    render(
      <AvatarGroup
        itemsSelected={[]}
        readOnly={true}
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );
    expect(screen.getByText('No existen datos')).toBeInTheDocument();
  });

  it('should not render dropdown when readOnly is true', () => {
    render(
      <AvatarGroup
        itemsSelected={[]}
        readOnly={true}
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );
    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();
  });

  it('should handle zero remaining users', () => {
    render(
      <AvatarGroup
        itemsSelected={[
          { id: '1', name: 'Test 1' },
          { id: '2', name: 'Test 2' },
        ]}
        maxToShow={3}
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );
    expect(screen.queryByText('+0')).not.toBeInTheDocument();
  });

  it('should render vertically when direction is vertical', () => {
    render(
      <AvatarGroup
        itemsSelected={[{ id: '1', name: 'Test 1' }, { id: '2', name: 'Test 2' }]}
        direction="vertical"
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );

    const avatarItem = screen.getAllByTestId('avatar-group-item')[1];
    const styles = window.getComputedStyle(avatarItem);

    expect(styles.left).toBe('0px');
    expect(styles.top).toBe('30px');
  });

  it('should render vertically when direction is horizontal', () => {
    render(
      <AvatarGroup
        itemsSelected={[{ id: '1', name: 'Test 1' }, { id: '2', name: 'Test 2' }]}
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );

    const avatarItem = screen.getAllByTestId('avatar-group-item')[1];
    const styles = window.getComputedStyle(avatarItem);

    expect(styles.left).toBe('30px');
    expect(styles.top).toBe('0px');
  });

  it('should render dropdown button', () => {
    render(
      <AvatarGroup
        itemsSelected={[]}
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );
    expect(screen.getByTestId('addbutton')).toBeInTheDocument();
  });

  it('should handle no options in dropdown', () => {
    render(
      <AvatarGroup
        itemsSelected={[]}
        avatarProps={mockAvatarProps}
        readOnly
        dropdownProps={{ ...mockDropdownProps, options: [] }}
      />,
    );

    expect(screen.getByText('No existen datos')).toBeInTheDocument();
  });

  it('should handle click on add button', () => {
    render(
      <AvatarGroup
        itemsSelected={[]}
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );

    fireEvent.click(screen.getByTestId('addbutton'));

    expect(screen.getByTestId('dropdown')).toBeVisible();
  });

  it('should render correct number of avatars based on maxToShow when itemsSelected is less than maxToShow', () => {
    render(
      <AvatarGroup
        itemsSelected={[
          { id: '1', name: 'Test 1' },
          { id: '2', name: 'Test 2' },
        ]}
        maxToShow={3}
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );
    const avatars = screen.getAllByTestId('avatar-group-item');
    expect(avatars.length).toBe(2);
  });

  it('should render no remaining count when remaining count is zero', () => {
    render(
      <AvatarGroup
        itemsSelected={[
          { id: '1', name: 'Test 1' },
          { id: '2', name: 'Test 2' },
          { id: '3', name: 'Test 3' },
        ]}
        maxToShow={3}
        avatarProps={mockAvatarProps}
        dropdownProps={mockDropdownProps}
      />,
    );
    expect(screen.queryByText('+0')).not.toBeInTheDocument();
    expect(screen.queryByText('+')).not.toBeInTheDocument();
  });

  it('should handle dropdownProps when it is defined', () => {
    const mockLocalDropdownProps = {
      options: [{ id: '1', name: 'Test 1' }],
      setSelectedOption: jest.fn(),
      dropDownClassName: 'test-class',
    };

    render(
      <AvatarGroup
        itemsSelected={[]}
        avatarProps={{ size: 50 }}
        dropdownProps={mockLocalDropdownProps}
      />,
    );

    const addButton = screen.getByTestId('addbutton');
    expect(addButton.className).toContain('test-class');
  });

  it('should pass correct size and shape to Avatar component', () => {
    render(
      <AvatarGroup
        itemsSelected={[{ id: '1', name: 'Test 1' }]}
        avatarProps={{ size: 80, shape: 'square' }}
        dropdownProps={{
          options: [{ id: '1', name: 'Test 1' }],
          setSelectedOption: jest.fn(),
        }}
      />,
    );

    const avatar = screen.getAllByTestId('avatar')[0];

    expect(avatar.getAttribute('size')).toBe('80');
    expect(avatar.getAttribute('shape')).toBe('square');
  });

  it('should use default size and shape when not provided', () => {
    render(
      <AvatarGroup
        itemsSelected={[{ id: '1', name: 'Test 1' }]}
        avatarProps={{ size: undefined }}
        dropdownProps={{
          options: [{ id: '1', name: 'Test 1' }],
          setSelectedOption: jest.fn(),
        }}
      />,
    );

    const avatar = screen.getAllByTestId('avatar')[0];

    expect(avatar.getAttribute('size')).toBeNull();
  });

  it('should handle undefined setSelectedOption in dropdownProps', () => {
    const dropdownPropsWithoutSetSelected = {
      options: [{ id: '1', name: 'Test 1' }],
    };

    render(
      <AvatarGroup
        itemsSelected={[]}
        avatarProps={mockAvatarProps}
        dropdownProps={dropdownPropsWithoutSetSelected}
      />,
    );

    const dropdown = screen.getByTestId('dropdown');
    expect(dropdown).toBeInTheDocument();
    expect(() => fireEvent.click(screen.getByTestId('addbutton'))).not.toThrow();
  });

  it('should handle undefined options in dropdownProps', () => {
    const dropdownPropsWithoutOptions = {
      setSelectedOption: jest.fn(),
    };

    render(
      <AvatarGroup
        itemsSelected={[]}
        avatarProps={mockAvatarProps}
        dropdownProps={dropdownPropsWithoutOptions}
      />,
    );

    expect(screen.getByTestId('avatar-group')).toBeInTheDocument();
  });

  it('should handle undefined avatarProps', () => {
    render(
      <AvatarGroup
        itemsSelected={[{ id: '1', name: 'No AvatarProps' }]}
        dropdownProps={mockDropdownProps}
      />,
    );

    expect(screen.getByTestId('avatar-group')).toBeInTheDocument();
  });
});
